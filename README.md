
### Node 版本要求
```
14.21.3 32
```

### python 版本要求
```
2.7.*
```

### Visual Studio (使用 C++ 的桌面开发) 版本要求
```
2017
可通过命令下载：npm install -global --production --verbose windows-build-tools
```

### npm 配置
```
npm config set registry https://registry.npmmirror.com/
npm config set node_sass_mirror https://registry.npmmirror.com/-/binary/node-sass/
npm config set electron_mirror https://registry.npmmirror.com/-/binary/electron/
npm config set electron_builder_binaries_mirror https://registry.npmmirror.com/-/binary/electron-builder-binaries/
npm config set atom_mirror https://registry.npmmirror.com/-/binary/atom/
npm config set sqlite3_mirror https://registry.npmmirror.com/-/binary/sqlite3/
npm config set sharp_libvips_mirror https://registry.npmmirror.com/-/binary/sharp-libvips/
npm config set sharp_mirror https://registry.npmmirror.com/-/binary/sharp/
npm config set fsevents_mirror https://registry.npmmirror.com/-/binary/fsevents/
```

### 下载依赖本地缓存

下载依赖本地缓存可以让 `npm run rebuild` 使用缓存的二进制文件，避免重复下载，提高构建速度。

#### 主要二进制依赖下载地址：
```
# Electron v10.1.5 (项目使用版本)
electron: https://registry.npmmirror.com/binary.html?path=electron/v10.1.5/

# SQLite3 二进制文件
sqlite3: https://registry.npmmirror.com/binary.html?path=sqlite3/

# Node-sass 二进制文件
node-sass: https://registry.npmmirror.com/binary.html?path=node-sass/

# Sharp 图像处理库
sharp: https://registry.npmmirror.com/binary.html?path=sharp/
sharp-libvips: https://registry.npmmirror.com/binary.html?path=sharp-libvips/

# 其他可能需要的二进制文件
fsevents: https://registry.npmmirror.com/binary.html?path=fsevents/
electron-builder-binaries: https://registry.npmmirror.com/binary.html?path=electron-builder-binaries/
```

#### Electron v10.1.5 缓存目录：
```
Windows: %USERPROFILE%\.electron\
Linux/Mac: ~/.electron/

具体文件路径示例：
Windows: C:\Users\<USER>\.electron\electron-v10.1.5-win32-ia32.zip
Linux: ~/.electron/electron-v10.1.5-linux-x64.zip
Mac: ~/.electron/electron-v10.1.5-darwin-x64.zip
```

#### 缓存使用说明：
1. **手动下载方式**：
   - 从上述镜像地址下载对应平台的Electron二进制文件
   - 创建 `%USERPROFILE%\.electron\` 目录（如果不存在）
   - 将下载的文件放置到该目录中
   - 文件名格式：`electron-v10.1.5-[platform]-[arch].zip`

2. **环境变量方式**：
   ```bash
   # 设置Electron缓存目录
   set ELECTRON_CACHE=%USERPROFILE%\.electron
   # 或者设置自定义缓存目录
   set ELECTRON_CACHE=D:\electron-cache
   ```

3. **验证缓存**：
   - 运行 `npm run rebuild` 时会优先使用本地缓存
   - 可以显著减少网络依赖和构建时间
   - 如果缓存文件存在，不会重新下载

### Project 依赖安装
```
npm i -d --force
npm i sqlite3@5.1.0 --build-from-source sqlite3已经改为4.2版本，不需要这个了
```

### Project 依赖安装后
```
npm run rebuild
npm run install-package-after sqlite3已经改为4.2版本，不需要这个了
```

### 开发环境运行命令
```
npm run dev:web
win: npm run dev:win
arm64: npm run dev:linux-arm64
amd64: npm run dev:linux-amd64
```

### 生产环境打包前置命令
```
客户端：npm run build:before
服务：npm run build:server
```
  
### 生产环境打包
```
npm run build:web
win: npm run build:win-exe
arm64: npm run build:linux-arm64-deb
amd64: npm run build:linux-amd64-deb
```

### 格式化
```
npm run lint
```

### 后台接口文档路径
```
/server/back/api/index
```

### sqlite3 数据库可视化工具 DB Browser
```
下载地址: https://github.com/sqlitebrowser/sqlitebrowser/releases/tag/v3.12.2
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
